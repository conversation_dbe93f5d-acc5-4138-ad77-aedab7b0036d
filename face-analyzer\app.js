const express = require('express');
const multer = require('multer');
const request = require('request');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// API Key from environment variable
const API_KEY = process.env.API_KEY || 'nXR2bq1yggUQP7NykVphMzSHOK9sGj0ErSbudVWUR58uT3DvtfmrnBN1eLF6zelw';
const BASE_URL = 'https://www.ailabapi.com';

// Skin Analysis Endpoint
app.post('/api/skin-analysis', upload.single('image'), (req, res) => {
  console.log('Received skin analysis request');

  if (!req.file) {
    console.log('No file provided');
    return res.status(400).json({ error: 'No image file provided' });
  }

  console.log('File received:', req.file.originalname);
  const imagePath = req.file.path;

  const options = {
    'method': 'POST',
    'url': `${BASE_URL}/api/portrait/analysis/skin-analysis-pro`,
    'headers': {
      'ailabapi-api-key': API_KEY
    },
    formData: {
      'image': {
        'value': fs.createReadStream(imagePath),
        'options': {
          'filename': req.file.originalname,
          'contentType': req.file.mimetype
        }
      }
    }
  };

  console.log('Making request to:', options.url);

  request(options, function (error, response) {
    // Clean up uploaded file
    fs.unlink(imagePath, (unlinkError) => {
      if (unlinkError) {
        console.error('Error deleting uploaded file:', unlinkError);
      }
    });

    if (error) {
      console.error('API Error:', error);
      return res.status(500).json({ error: 'Failed to analyze skin', details: error.message });
    }

    console.log('Response status:', response.statusCode);
    console.log('Response body:', response.body);

    try {
      const result = JSON.parse(response.body);
      res.json(result);
    } catch (parseError) {
      console.error('Parse Error:', parseError);
      console.error('Raw response body:', response.body);
      res.status(500).json({ error: 'Failed to parse API response', details: parseError.message, rawResponse: response.body });
    }
  });
});

// Serve test page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'test.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Face Analyzer API is running' });
});

// Start server
app.listen(PORT, () => {
  console.log('='.repeat(50));
  console.log(`Face Analyzer API server is running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`Skin analysis endpoint: POST http://localhost:${PORT}/api/skin-analysis`);
  console.log(`Test page: http://localhost:${PORT}/`);
  console.log('='.repeat(50));
});

module.exports = app;