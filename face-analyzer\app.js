const express = require('express');
const multer = require('multer');
const request = require('request');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// API Key from environment variable
const API_KEY = process.env.API_KEY || 'nXR2bq1yggUQP7NykVphMzSHOK9sGj0ErSbudVWUR58uT3DvtfmrnBN1eLF6zelw';
const BASE_URL = 'https://www.ailabapi.com';

// Skin Analysis Endpoint
app.post('/api/skin-analysis', upload.single('image'), (req, res) => {
  console.log('Received skin analysis request');

  if (!req.file) {
    console.log('No file provided');
    return res.status(400).json({ error: 'No image file provided' });
  }

  console.log('File received:', req.file.originalname);
  console.log('File size:', req.file.size, 'bytes');
  console.log('File type:', req.file.mimetype);

  // Validate file size (max 8MB as per API requirements)
  if (req.file.size > 8 * 1024 * 1024) {
    return res.status(400).json({ error: 'File size too large. Maximum 8MB allowed.' });
  }

  // Validate file type
  if (!['image/jpeg', 'image/jpg'].includes(req.file.mimetype)) {
    return res.status(400).json({ error: 'Invalid file type. Only JPEG/JPG images are supported.' });
  }

  const imagePath = req.file.path;

  const options = {
    'method': 'POST',
    'url': `${BASE_URL}/api/portrait/analysis/skin-analysis-pro`,
    'headers': {
      'ailabapi-api-key': API_KEY
    },
    formData: {
      'image': {
        'value': fs.createReadStream(imagePath),
        'options': {
          'filename': req.file.originalname,
          'contentType': req.file.mimetype
        }
      }
    }
  };

  console.log('Making request to:', options.url);

  request(options, function (error, response) {
    // Clean up uploaded file
    fs.unlink(imagePath, (unlinkError) => {
      if (unlinkError) {
        console.error('Error deleting uploaded file:', unlinkError);
      }
    });

    if (error) {
      console.error('API Error:', error);
      return res.status(500).json({ error: 'Failed to analyze skin', details: error.message });
    }

    console.log('Response status:', response.statusCode);
    console.log('Response body:', response.body);

    try {
      const result = JSON.parse(response.body);

      // Check if the API returned an error
      if (result.error_code && result.error_code !== 0) {
        console.error('API returned error:', result);

        // Provide more specific error messages based on error code
        let userMessage = 'Skin analysis failed';
        if (result.error_code_str === 'PROCESSING_FAILURE') {
          userMessage = 'Unable to analyze the image. Please ensure the image contains a clear, well-lit face and try again.';
        }

        return res.status(400).json({
          error: userMessage,
          details: result.error_msg,
          apiError: result
        });
      }

      res.json(result);
    } catch (parseError) {
      console.error('Parse Error:', parseError);
      console.error('Raw response body:', response.body);
      res.status(500).json({ error: 'Failed to parse API response', details: parseError.message, rawResponse: response.body });
    }
  });
});

// Serve test page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'test.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Face Analyzer API is running' });
});

// Start server
app.listen(PORT, () => {
  console.log('='.repeat(50));
  console.log(`Face Analyzer API server is running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`Skin analysis endpoint: POST http://localhost:${PORT}/api/skin-analysis`);
  console.log(`Test page: http://localhost:${PORT}/`);
  console.log('='.repeat(50));
});

module.exports = app;