<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skin Analysis Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>Skin Analysis Test</h1>
    
    <div class="upload-area">
        <input type="file" id="imageInput" accept="image/*" style="display: none;">
        <p>Click to select an image or drag and drop</p>
        <button onclick="document.getElementById('imageInput').click()">Select Image</button>
    </div>
    
    <div>
        <button id="analyzeBtn" onclick="analyzeSkin()" disabled>Analyze Skin</button>
    </div>
    
    <div class="loading" id="loading">
        <p>Analyzing image... Please wait.</p>
    </div>
    
    <div class="result" id="result" style="display: none;"></div>

    <script>
        const imageInput = document.getElementById('imageInput');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const loading = document.getElementById('loading');
        const result = document.getElementById('result');

        imageInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                analyzeBtn.disabled = false;
                analyzeBtn.textContent = `Analyze: ${e.target.files[0].name}`;
            }
        });

        async function analyzeSkin() {
            const file = imageInput.files[0];
            if (!file) {
                alert('Please select an image first');
                return;
            }

            const formData = new FormData();
            formData.append('image', file);

            analyzeBtn.disabled = true;
            loading.style.display = 'block';
            result.style.display = 'none';

            try {
                const response = await fetch('/api/skin-analysis', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    result.textContent = JSON.stringify(data, null, 2);
                    result.style.backgroundColor = '#d4edda';
                } else {
                    // Show user-friendly error message
                    let errorMessage = data.error || 'Unknown error occurred';
                    if (data.details) {
                        errorMessage += `\n\nDetails: ${data.details}`;
                    }

                    // Add helpful tips for common errors
                    if (data.error && data.error.includes('clear, well-lit face')) {
                        errorMessage += '\n\nTips:\n• Use a high-quality image with good lighting\n• Ensure the face is clearly visible and not obscured\n• Face should be looking mostly forward (not too much side angle)\n• Image should be at least 400x400 pixels';
                    }

                    result.textContent = errorMessage;
                    result.style.backgroundColor = '#f8d7da';
                }
                
                result.style.display = 'block';
            } catch (error) {
                result.textContent = `Network Error: ${error.message}`;
                result.style.backgroundColor = '#f8d7da';
                result.style.display = 'block';
            } finally {
                loading.style.display = 'none';
                analyzeBtn.disabled = false;
                analyzeBtn.textContent = 'Analyze Skin';
            }
        }

        // Drag and drop functionality
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            
            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                imageInput.files = files;
                analyzeBtn.disabled = false;
                analyzeBtn.textContent = `Analyze: ${files[0].name}`;
            }
        });
    </script>
</body>
</html>
